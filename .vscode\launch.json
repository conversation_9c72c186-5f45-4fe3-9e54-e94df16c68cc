{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/libraries/project_2025_G_KQF_IIR_new/project_2025_G_dac_reco_Fs_hs/project_2025_G_dac_reco_Fs/project_2025_G_dac/project_2025_G/.vscode", "program": "d:/libraries/project_2025_G_KQF_IIR_new/project_2025_G_dac_reco_Fs_hs/project_2025_G_dac_reco_Fs/project_2025_G_dac/project_2025_G/.vscode/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}